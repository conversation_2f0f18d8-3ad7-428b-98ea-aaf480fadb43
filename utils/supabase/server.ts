import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from 'next/headers';
import { Database } from '@/types/db';

export function createClient() {
  const cookieStore = cookies()

  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, {
                ...options,
                // 优化Cookie设置
                sameSite: 'lax',
                secure: process.env.NODE_ENV === 'production',
                httpOnly: false, // 客户端需要访问
              })
            )
          } catch (error) {
            console.error('Error setting cookies:', error);
          }
        },
      },
    }
  )
}
