{"_meta": {"template_version": 0}, "fixtures": [{"name": "prod_basic", "path": "/v1/products", "method": "post", "params": {"name": "Starter", "description": "Kickstart your journey with essential templates and community access.", "metadata": {"index": "0"}}}, {"name": "price_starter_month", "path": "/v1/prices", "method": "post", "params": {"product": "${prod_basic:id}", "currency": "usd", "billing_scheme": "per_unit", "unit_amount": 1900, "recurring": {"interval": "month", "interval_count": 1}}}, {"name": "price_starter_year", "path": "/v1/prices", "method": "post", "params": {"product": "${prod_basic:id}", "currency": "usd", "billing_scheme": "per_unit", "unit_amount": 19000, "recurring": {"interval": "year", "interval_count": 1}}}, {"name": "prod_pro", "path": "/v1/products", "method": "post", "params": {"name": "Pro", "description": "For those who need advanced templates and enhanced community engagement.", "metadata": {"index": "1"}}}, {"name": "price_pro_month", "path": "/v1/prices", "method": "post", "params": {"product": "${prod_pro:id}", "currency": "usd", "billing_scheme": "per_unit", "unit_amount": 4900, "recurring": {"interval": "month", "interval_count": 1}}}, {"name": "price_pro_year", "path": "/v1/prices", "method": "post", "params": {"product": "${prod_pro:id}", "currency": "usd", "billing_scheme": "per_unit", "unit_amount": 49000, "recurring": {"interval": "year", "interval_count": 1}}}, {"name": "prod_enterprise", "path": "/v1/products", "method": "post", "params": {"name": "Enterprise", "description": "For organizations that require comprehensive templates and dedicated support.", "metadata": {"index": "3"}}}, {"name": "price_enterprise_month", "path": "/v1/prices", "method": "post", "params": {"product": "${prod_enterprise:id}", "currency": "usd", "billing_scheme": "per_unit", "unit_amount": 39900, "recurring": {"interval": "month", "interval_count": 1}}}, {"name": "price_enterprise_year", "path": "/v1/prices", "method": "post", "params": {"product": "${prod_enterprise:id}", "currency": "usd", "billing_scheme": "per_unit", "unit_amount": 399000, "recurring": {"interval": "year", "interval_count": 1}}}]}