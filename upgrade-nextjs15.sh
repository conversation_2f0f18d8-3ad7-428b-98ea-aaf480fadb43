#!/bin/bash

echo "🚀 Next.js 15 升级脚本"
echo "====================="

# 1. 运行官方升级工具
echo "⚡ 升级 Next.js 和依赖..."
npx @next/codemod@canary upgrade latest

# 2. 升级 React 到 19
echo "📦 升级 React..."
npm install react@19 react-dom@19

# 3. 迁移异步 API
echo "🔄 迁移异步 API..."
npx @next/codemod@canary next-async-request-api .

# 4. 安装依赖
echo "📦 安装依赖..."
npm install

# 5. 检查构建
echo "🏗️ 测试构建..."
npm run build

echo "✅ 升级完成！运行 npm run dev 测试"