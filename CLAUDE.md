# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🛠️ 常用开发命令

### 核心开发命令
- `pnpm dev` - 启动开发服务器
- `pnpm lint` - 运行 ESLint 检查
- `pnpm prettier-fix` - 自动格式化代码

### 数据库相关 (Supabase)
- `pnpm supabase:start` - 启动本地 Supabase
- `pnpm supabase:stop` - 停止本地 Supabase
- `pnpm supabase:reset` - 重置数据库
- `pnpm supabase:generate-types` - 生成数据库类型定义
- `pnpm supabase:generate-migration` - 创建新的迁移文件

### Stripe 相关
- `pnpm stripe:listen` - 监听 Stripe webhook
- `pnpm stripe:fixtures` - 加载测试数据

## 🏗️ 项目架构概览

### 技术栈
- **框架**: Next.js 14 (计划升级到 15)
- **语言**: TypeScript
- **样式**: Tailwind CSS + Radix UI
- **数据库**: Supabase (PostgreSQL)
- **支付**: Stripe
- **API**: tRPC
- **国际化**: i18next (支持 en/zh/ja)
- **状态管理**: React Query + tPC

### 核心目录结构
```
app/                    # Next.js App Router
├── (auth_forms)/       # 认证相关页面
├── (dashboard)/        # 仪表板页面
├── api/               # API 路由
└── layout.tsx         # 根布局
components/            # React 组件
├── ui/               # 基础 UI 组件
├── landing-page/     # 首页组件
└── dashboard/        # 仪表板组件
lib/                  # 工具库
├── i18n.ts          # 国际化配置
└── utils.ts         # 通用工具函数
server/               # 服务端代码
└── api/             # tRPC 路由
trpc/                # tRPC 客户端配置
utils/               # 实用工具
├── supabase/        # Supabase 客户端
└── stripe/          # Stripe 工具
```

### 关键架构特点

1. **认证系统**: 使用 Supabase Auth，支持邮箱/密码登录
2. **支付集成**: Stripe 订阅系统，支持 webhook 同步
3. **国际化**: 支持中英日三语，使用 i18next
4. **文档系统**: 基于 Fumadocs 的 MDX 文档
5. **类型安全**: 完整的 TypeScript 类型定义，包括数据库类型

### 数据库架构
- 使用 Supabase 作为后端
- 主要表: users, subscriptions, posts 等
- 迁移文件位于 `supabase/migrations/`

### API 架构
- 主要使用 tRPC 提供 API
- 传统 API 路由用于 webhook 和特定功能
- 路由位于 `app/api/` 和 `server/api/`

## 📋 开发注意事项

1. **国际化**: 所有用户界面文本都应使用 i18n 系统，避免硬编码
2. **类型安全**: 使用 Supabase 生成的类型定义确保数据库操作安全
3. **组件复用**: 优先使用 `components/ui/` 中的基础组件
4. **样式规范**: 使用 Tailwind CSS，遵循设计系统
5. **性能优化**: 使用 React Query 进行数据缓存和状态管理

## 🔄 当前状态

项目正在从开源模板转变为 AI 笔记应用，品牌已更新为 AlsonNote。正在进行国际化支持的开发，支持中文、英文和日文。