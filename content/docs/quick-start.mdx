---
title: Quick Start
description: Getting Started with Hikari
icon: Album
---

## Dependencies

This package uses the following softwares and tools. You will need to install them before you can get started:

- **PNPM** - Package Manager
- **Docker** - Containerization. Don't worry you won't touch this very much. _wink wink_
- **Stripe CLI** - Command Line Interface for Stripe
- **Supabase CLI** - Command Line Interface for Supabase

<div className="steps">
  <div className="step">
    **Install PNPM**: Download and install PNPM from
    [here](https://pnpm.io/installation), or install it using npm or brew:
    ```bash title="terminal" npm install -g pnpm # brew install pnpm ```
  </div>
  <div className="step">
    **Install Docker**: Download and install Docker from
    [here](https://www.docker.com/get-started/).
  </div>
  <div className="step">
    **Install Stripe CLI**: Download and install Stripe CLI from
    [here](https://stripe.com/docs/stripe-cli).
  </div>
  <div className="step">
    **Install Supabase CLI**: Download and install Supabase CLI from
    [here](https://supabase.com/docs/guides/cli/getting-started#installing-the-supabase-cli).
  </div>
</div>

## Configuring your Environment Variables

<Cards>
  <Card
    title="Configure Supabase"
    href="/docs/configure/supabase"
    description="Learn how to develop with supabase locally and in production"
  />
  <Card
    title="Configure Stripe"
    href="/docs/configure/stripe"
    description="Learn how to use stripe in your SaaS application"
  />
</Cards>
