{"name": "<PERSON><PERSON><PERSON>", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prettier-fix": "prettier --write .", "stripe:login": "stripe login", "stripe:listen": "stripe listen --forward-to=localhost:3000/api/webhooks", "stripe:fixtures": "stripe fixtures utils/stripe/fixtures/stripe-fixtures.json", "supabase:start": "npx supabase start", "supabase:stop": "npx supabase stop", "supabase:status": "npx supabase status", "supabase:restart": "npm run supabase:stop && npm run supabase:start", "supabase:reset": "npx supabase db reset", "supabase:link": "npx supabase link", "supabase:generate-types": "npx supabase gen types typescript --local --schema public > types_db.ts", "supabase:generate-migration": "npx supabase db diff | npx supabase migration new", "supabase:generate-seed": "npx supabase db dump --data-only -f supabase/seed.sql", "supabase:push": "npx supabase db push", "supabase:pull": "npx supabase db pull"}, "dependencies": {"@heroicons/react": "^2.1.5", "@hookform/resolvers": "^3.6.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@radix-ui/themes": "^3.1.1", "@stripe/stripe-js": "2.4.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.5.0", "@supabase/supabase-js": "^2.45.1", "@t3-oss/env-nextjs": "^0.4.0", "@tailwindcss/typography": "^0.5.13", "@tanstack/react-query": "^5.51.24", "@tanstack/react-table": "^8.19.2", "@trpc/client": "^11.0.0-rc.446", "@trpc/next": "^10.45.2", "@trpc/react-query": "^11.0.0-rc.446", "@trpc/server": "^11.0.0-rc.446", "@types/mdx": "^2.0.13", "@vercel/analytics": "^1.3.1", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.1.5", "framer-motion": "^11.2.13", "fumadocs-core": "12.4.1", "fumadocs-mdx": "^8.2.33", "fumadocs-ui": "12.4.1", "i18next": "^25.4.0", "i18next-browser-languagedetector": "^8.2.0", "input-otp": "^1.2.4", "lucide-react": "^0.376.0", "next": "14.2.3", "next-themes": "^0.3.0", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.0", "react-i18next": "^15.7.1", "react-markdown": "^9.0.1", "react-merge-refs": "^2.1.1", "react-use-gesture": "^9.1.3", "remark-gfm": "^4.0.0", "sonner": "^1.5.0", "stripe": "^14.25.0", "styled-components": "^6.1.11", "superjson": "^2.2.1", "tailwind-merge": "^2.3.0", "tailwindcss": "^3.4.4", "tailwindcss-animate": "^1.0.7", "uuid": "^10.0.0", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.14.2", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-next": "14.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.34.2", "eslint-plugin-tailwindcss": "^3.17.3", "postcss": "^8.4.38", "prettier": "^3.3.1", "prettier-plugin-tailwindcss": "^0.5.14", "typescript": "^5.4.5"}}