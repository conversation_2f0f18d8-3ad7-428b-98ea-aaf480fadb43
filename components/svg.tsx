import * as React from 'react';
import { SVGProps } from 'react';

export const StripeSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlSpace="preserve"
    id="Layer_1"
    x={0}
    y={0}
    fill="currentColor"
    viewBox="0 0 450 222.5"
    {...props}
  >
    <path d="M414 113.4c0-25.6-12.4-45.8-36.1-45.8-23.8 0-38.2 20.2-38.2 45.6 0 30.1 17 45.3 41.4 45.3 11.9 0 20.9-2.7 27.7-6.5v-20c-6.8 3.4-14.6 5.5-24.5 5.5-9.7 0-18.3-3.4-19.4-15.2h48.9c0-1.3.2-6.5.2-8.9zm-49.4-9.5c0-11.3 6.9-16 13.2-16 6.1 0 12.6 4.7 12.6 16h-25.8zM301.1 67.6c-9.8 0-16.1 4.6-19.6 7.8l-1.3-6.2h-22v116.6l25-5.3.1-28.3c3.6 2.6 8.9 6.3 17.7 6.3 17.9 0 34.2-14.4 34.2-46.1-.1-29-16.6-44.8-34.1-44.8zm-6 68.9c-5.9 0-9.4-2.1-11.8-4.7l-.1-37.1c2.6-2.9 6.2-4.9 11.9-4.9 9.1 0 15.4 10.2 15.4 23.3 0 13.4-6.2 23.4-15.4 23.4zM223.8 61.7l25.1-5.4V36l-25.1 5.3zM223.8 69.3h25.1v87.5h-25.1zM196.9 76.7l-1.6-7.4h-21.6v87.5h25V97.5c5.9-7.7 15.9-6.3 19-5.2v-23c-3.2-1.2-14.9-3.4-20.8 7.4zM146.9 47.6l-24.4 5.2-.1 80.1c0 14.8 11.1 25.7 25.9 25.7 8.2 0 14.2-1.5 17.5-3.3V135c-3.2 1.3-19 5.9-19-8.9V90.6h19V69.3h-19l.1-21.7zM79.3 94.7c0-3.9 3.2-5.4 8.5-5.4 7.6 0 17.2 2.3 24.8 6.4V72.2c-8.3-3.3-16.5-4.6-24.8-4.6C67.5 67.6 54 78.2 54 95.9c0 27.6 38 23.2 38 35.1 0 4.6-4 6.1-9.6 6.1-8.3 0-18.9-3.4-27.3-8v23.8c9.3 4 18.7 5.7 27.3 5.7 20.8 0 35.1-10.3 35.1-28.2-.1-29.8-38.2-24.5-38.2-35.7z" />
  </svg>
);

export const NextjsSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={300}
    height={60}
    viewBox="0 0 394 80"
    fill="none"
    {...props}
  >
    <path
      fill="currentColor"
      d="M261.919.033h68.628V12.7h-27.224v66.639H289.71V12.7h-27.791zM149.052.033V12.7h-55.01v20.377h44.239v12.667H94.042v20.928h55.01V79.34H80.43V12.7h-.006V.033ZM183.32.066h-17.814l63.806 79.306h17.866l-31.907-39.626L247.127.126l-17.815.028-22.96 28.516ZM201.6 56.715l-8.921-11.092-27.224 33.81h17.865Z"
    />
    <path
      fill="currentColor"
      fillRule="evenodd"
      d="M80.907 79.339 17.015 0H0v79.306h13.612V16.952l50.195 62.387Z"
      clipRule="evenodd"
    />
    <path
      fill="currentColor"
      d="M333.607 78.855a3.528 3.528 0 0 1-2.555-1.036c-.71-.691-1.061-1.527-1.052-2.518-.009-.963.342-1.79 1.052-2.481a3.528 3.528 0 0 1 2.555-1.036c.959 0 1.798.345 2.508 *********** 1.079 1.518 1.089 2.481a3.44 3.44 0 0 1-.508 1.79 3.675 3.675 0 0 1-1.319 1.282 3.403 3.403 0 0 1-1.77.482zM356.84 45.445h6.032v23.24c-.009 2.135-.471 3.962-1.374 5.498-.913 1.536-2.177 2.708-3.8 3.535-1.614.818-3.505 1.237-5.654 1.237-1.965 0-3.726-.355-5.294-1.046-1.568-.69-2.813-1.726-3.726-3.09-.923-1.363-1.375-3.063-1.375-5.098h6.042c.009.89.212 1.663.599 2.308a3.855 3.855 0 0 0 1.605 1.482c.691.345 1.485.518 2.379.518.969 0 1.799-.2 2.472-.61.673-.4 1.19-1 1.55-1.799.35-.79.535-1.772.544-2.935zM387.691 54.534c-.147-1.409-.793-2.509-1.918-3.29-1.135-.79-2.601-1.182-4.4-1.182-1.263 0-2.351.191-3.255.564-.904.382-1.605.89-2.085 1.536-.479.645-.719 1.381-.738 2.208 0 .691.166 1.29.489 1.79.323.51.756.937 1.319 1.282a8.806 8.806 0 0 0 1.845.882c.682.236 1.365.436 2.047.6l3.145.772a21.74 21.74 0 0 1 3.662 1.182 12.966 12.966 0 0 1 3.163 1.872 8.384 8.384 0 0 1 2.214 2.726c.544 1.064.821 2.309.821 3.745 0 1.936-.498 3.635-1.504 5.107-1.005 1.464-2.453 2.609-4.353 3.436-1.891.818-4.178 1.236-6.871 1.236-2.601 0-4.87-.4-6.779-1.2-1.918-.79-3.413-1.954-4.492-3.48-1.079-1.527-1.66-3.39-1.743-5.58h5.977c.083 1.144.452 2.099 1.079 2.871.636.764 1.466 1.327 2.481 1.709 1.024.372 2.167.563 3.431.563 1.319 0 2.481-.2 3.486-.59.996-.391 1.78-.937 2.343-1.645.572-.7.858-1.527.867-2.473-.009-.863-.268-1.581-.766-2.145-.507-.563-1.208-1.036-2.103-1.417a21.606 21.606 0 0 0-3.154-1.027l-3.818-.964c-2.758-.7-4.944-1.763-6.54-3.19-1.604-1.427-2.398-3.317-2.398-5.69 0-1.944.535-3.653 1.615-5.116 1.069-1.463 2.536-2.6 4.39-3.408 1.863-.818 3.966-1.218 6.308-1.218 2.38 0 4.464.4 6.263 1.218 1.798.809 3.21 1.936 4.233 3.372 1.024 1.436 1.559 3.08 1.587 4.944z"
    />
  </svg>
);

export const SupabaseSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={2500}
    height={650}
    viewBox="0 0 2978 628"
    fill="none"
    {...props}
  >
    <path
      fill="currentColor"
      d="M717 390.353c3.036 28.368 28.841 76.998 103.725 76.998 65.27 0 96.641-41.538 96.641-82.063 0-36.473-24.793-66.36-73.873-76.492l-35.418-7.598c-13.661-2.533-22.769-10.131-22.769-22.289 0-14.184 14.168-24.821 31.877-24.821 28.334 0 38.96 18.742 40.984 33.433l56.163-12.664c-3.036-26.848-26.817-71.932-97.653-71.932-53.633 0-93.099 36.979-93.099 81.556 0 34.953 21.757 63.828 69.824 74.465l32.888 7.599c19.227 4.052 26.817 13.171 26.817 24.315 0 13.171-10.625 24.822-32.888 24.822-29.347 0-44.02-18.237-45.538-37.993L717 390.353ZM1132.42 459.753h64.26c-1.01-8.612-2.53-25.835-2.53-45.085V210.523h-67.3V355.4c0 28.875-17.2 49.137-47.05 49.137-31.37 0-45.54-22.289-45.54-50.15V210.523h-67.293v157.542c0 54.202 34.403 98.273 94.613 98.273 26.31 0 55.16-10.131 68.82-33.433 0 10.131 1.01 21.782 2.02 26.848ZM1333.26 556V435.438c12.14 16.716 37.44 30.394 71.85 30.394 70.33 0 117.38-55.723 117.38-131.201 0-73.958-41.99-129.68-114.85-129.68-37.44 0-65.27 16.716-76.4 35.966v-30.394h-65.27V556h67.29Zm122.95-220.862c0 44.578-27.32 70.412-61.73 70.412-34.4 0-62.23-26.341-62.23-70.412 0-44.071 27.83-69.906 62.23-69.906 34.41 0 61.73 25.835 61.73 69.906ZM1560.6 391.873c0 39.006 32.38 74.972 85.51 74.972 36.93 0 60.71-17.224 73.36-36.98 0 9.625 1.01 23.302 2.53 29.888h61.73c-1.52-8.612-3.03-26.342-3.03-39.512V297.652c0-50.15-29.35-94.727-108.28-94.727-66.79 0-102.72 43.057-106.76 82.063l59.7 12.664c2.03-21.782 18.22-40.525 47.56-40.525 28.34 0 42 14.69 42 32.42 0 8.612-4.55 15.704-18.72 17.73l-61.22 9.118c-41.49 6.079-74.38 30.9-74.38 75.478Zm99.67 24.822c-21.75 0-32.38-14.184-32.38-28.874 0-19.25 13.66-28.875 30.87-31.407l56.16-8.612v11.144c0 44.072-26.31 57.749-54.65 57.749ZM1913.8 459.753v-30.394c13.15 21.276 39.46 36.473 73.87 36.473 70.84 0 117.39-56.229 117.39-131.707 0-73.959-42-130.187-114.86-130.187-36.94 0-64.26 16.21-75.39 33.939V93h-66.28v366.753h65.27Zm123.96-125.122c0 45.591-27.32 70.919-61.73 70.919-33.9 0-62.23-25.834-62.23-70.919 0-45.591 28.33-70.412 62.23-70.412 34.41 0 61.73 24.821 61.73 70.412ZM2143.16 391.873c0 39.006 32.38 74.972 85.51 74.972 36.93 0 60.72-17.224 73.36-36.98 0 9.625 1.02 23.302 2.53 29.888h61.73c-1.51-8.612-3.03-26.342-3.03-39.512V297.652c0-50.15-29.35-94.727-108.28-94.727-66.79 0-102.71 43.057-106.76 82.063l59.7 12.664c2.03-21.782 18.22-40.525 47.56-40.525 28.34 0 42 14.69 42 32.42 0 8.612-4.55 15.704-18.72 17.73l-61.22 9.118c-41.49 6.079-74.38 30.9-74.38 75.478Zm99.68 24.822c-21.76 0-32.39-14.184-32.39-28.874 0-19.25 13.66-28.875 30.87-31.407l56.16-8.612v11.144c0 44.072-26.31 57.749-54.64 57.749ZM2409.84 390.353c3.03 28.368 28.84 76.998 103.72 76.998 65.27 0 96.64-41.538 96.64-82.063 0-36.473-24.79-66.36-73.87-76.492l-35.42-7.598c-13.66-2.533-22.76-10.131-22.76-22.289 0-14.184 14.16-24.821 31.87-24.821 28.34 0 38.96 18.742 40.99 33.433l56.16-12.664c-3.04-26.848-26.82-71.932-97.65-71.932-53.64 0-93.1 36.979-93.1 81.556 0 34.953 21.75 63.828 69.82 74.465l32.89 7.599c19.23 4.052 26.82 13.171 26.82 24.315 0 13.171-10.63 24.822-32.89 24.822-29.35 0-44.02-18.237-45.54-37.993l-57.68 12.664ZM2712.4 306.77c1.52-22.795 20.75-49.137 55.66-49.137 38.45 0 54.65 24.316 55.66 49.137H2712.4Zm117.9 64.841c-8.1 22.288-25.3 37.992-56.67 37.992-33.4 0-61.23-23.809-62.74-56.735h178.1c0-1.014 1.01-11.145 1.01-20.77 0-80.037-46.04-129.173-122.95-129.173-63.75 0-122.45 51.669-122.45 131.2 0 84.089 60.21 133.226 128.52 133.226 61.22 0 100.69-35.966 113.34-79.024l-56.16-16.716ZM44.45 372.395c-14.608 0-26.45-11.839-26.45-26.442a26.32 26.32 0 0 1 5.194-15.74l10.187-14.3L264.276 4.145A10.063 10.063 0 0 1 272.424 0c5.565 0 10.076 4.51 10.076 10.073v362.322H44.45ZM520.55 237.854c14.608 0 26.45 11.839 26.45 26.443 0 5.896-1.93 11.341-5.194 15.739l-10.187 14.3-230.895 311.768a10.066 10.066 0 0 1-8.148 4.145c-5.565 0-10.076-4.51-10.076-10.073V237.854h238.05Z"
    />
    <path
      fill="currentColor"
      d="M520.55 237.854c14.608 0 26.45 11.839 26.45 26.443 0 5.896-1.93 11.341-5.194 15.739l-10.187 14.3-230.895 311.768a10.066 10.066 0 0 1-8.148 4.145c-5.565 0-10.076-4.51-10.076-10.073V237.854h238.05Z"
    />
  </svg>
);

export const VercelSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={100}
    height={20}
    fill="none"
    viewBox="0 0 100 20"
    {...props}
  >
    <path
      fill="currentColor"
      d="m11.563 0 11.562 20H0L11.563 0ZM49.875 10.625c0-3.219-2.375-5.469-5.781-5.469-3.407 0-5.782 2.25-5.782 5.469 0 3.156 2.563 5.469 6.094 5.469 1.938 0 3.688-.719 4.813-2.032l-2.125-1.218c-.657.656-1.625 1.062-2.688 1.062-1.562 0-2.906-.844-3.375-2.125l-.093-.219h8.843c.063-.312.094-.624.094-.937Zm-8.969-.938.063-.187c.406-1.344 1.593-2.156 3.093-2.156 1.532 0 2.688.812 3.094 2.156l.063.188h-6.313ZM83.531 10.625c0-3.219-2.375-5.469-5.781-5.469-3.406 0-5.781 2.25-5.781 5.469 0 3.156 2.562 5.469 6.094 5.469 1.937 0 3.687-.719 4.812-2.032l-2.125-1.218c-.656.656-1.625 1.062-2.688 1.062-1.562 0-2.906-.844-3.374-2.125l-.094-.219h8.844a4.77 4.77 0 0 0 .093-.937Zm-8.969-.938.063-.187c.406-1.344 1.594-2.156 3.094-2.156 1.531 0 2.687.812 3.094 2.156l.062.188h-6.313ZM68.531 8.844l2.125-1.219c-1-1.563-2.781-2.438-4.937-2.438-3.406 0-5.781 2.25-5.781 5.47 0 3.218 2.374 5.468 5.78 5.468 2.157 0 3.938-.875 4.938-2.438l-2.125-1.218c-.562.937-1.562 1.469-2.812 1.469-1.969 0-3.281-1.313-3.281-3.282s1.312-3.281 3.28-3.281c1.22 0 2.25.531 2.813 1.469ZM88.219 1.75h-2.5v14.063h2.5V1.75ZM40.156 1.75h-2.875l-5.5 9.5-5.5-9.5h-2.906l8.406 14.5 8.375-14.5ZM57.844 8.063c.281 0 .562.03.843.093V5.5c-2.124.063-4.124 1.25-4.124 2.719V5.5h-2.5v10.313h2.5v-4.47c0-1.937 1.343-3.28 3.28-3.28Z"
    />
  </svg>
);

export const GithubSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 2880 1024"
    {...props}
  >
    <path
      fill="currentColor"
      fillRule="evenodd"
      d="M1185.92 769.928h-1.28c.576 0 .96.576 1.536.704.064 0 .32-.064.384-.064l-.64-.64zm.256.704c-5.952.064-20.928 3.136-36.736 3.136-49.92 0-67.2-23.042-67.2-53.122v-200.32H1184c5.76 0 10.24-5.12 10.24-12.16v-108.8c0-5.76-5.12-10.88-10.24-10.88h-101.76v-135.04c0-5.12-3.2-8.32-8.96-8.32H935.04c-5.76 0-8.96 3.2-8.96 8.32v138.88s-69.76 17.28-74.24 17.92c-5.12 1.28-8.32 5.76-8.32 10.88v87.04c0 7.04 5.12 12.16 10.88 12.16h71.04v209.92c0 156.162 108.8 172.162 183.04 172.162 33.92 0 74.88-10.88 81.28-14.08 3.84-1.28 5.76-5.76 5.76-10.24v-96c0-6.336-4.352-10.56-9.344-11.456zM2702.72 629.766c0-115.84-46.72-131.2-96-126.08-38.4 2.56-69.12 21.76-69.12 21.76v225.28s31.36 21.762 78.08 23.042c65.92 1.92 87.04-21.762 87.04-144.002zm155.52-10.24c0 219.522-71.04 282.242-195.2 282.242-104.96 0-161.28-53.12-161.28-53.12s-2.56 29.44-5.76 33.28c-1.92 3.84-5.12 5.12-8.96 5.12h-94.72c-6.4 0-12.16-5.12-12.16-10.88l1.28-711.042c0-5.76 5.12-10.88 10.88-10.88h136.32c5.76 0 10.88 5.12 10.88 10.88v241.28s52.48-33.92 129.28-33.92l-.64-1.28c76.8 0 190.08 28.8 190.08 248.32zm-558.08-231.04h-134.4c-7.04 0-10.88 5.12-10.88 12.16v348.16s-35.2 24.962-83.2 24.962-62.08-21.762-62.08-69.762v-304c0-5.76-5.12-10.88-10.88-10.88h-136.96c-5.76 0-10.88 5.12-10.88 10.88v327.04c0 140.802 78.72 176.002 186.88 176.002 88.96 0 161.28-49.28 161.28-49.28s3.2 24.96 5.12 28.8c1.28 3.2 5.76 5.76 10.24 5.76h85.76c7.04 0 10.88-5.12 10.88-10.88l1.28-478.082c0-5.76-5.12-10.88-12.16-10.88zm-1516.8-.64H647.04c-5.76 0-10.88 5.76-10.88 12.8v469.762c0 12.8 8.32 17.28 19.2 17.28h122.88c12.8 0 16-5.76 16-17.28V398.726c0-5.76-5.12-10.88-10.88-10.88zm-67.2-216.32c-49.28 0-88.32 39.04-88.32 88.32 0 49.28 39.04 88.32 88.32 88.32 48 0 87.04-39.04 87.04-88.32 0-49.28-39.04-88.32-87.04-88.32zm1055.36-16h-135.04c-5.76 0-10.88 5.12-10.88 10.88v261.76h-211.84v-261.76c0-5.76-5.12-10.88-10.88-10.88h-136.32c-5.76 0-10.88 5.12-10.88 10.88v711.042c0 5.76 5.76 10.88 10.88 10.88h136.32c5.76 0 10.88-5.12 10.88-10.88V573.446h211.84l-1.28 304.002c0 5.76 5.12 10.88 10.88 10.88h136.32c5.76 0 10.88-5.12 10.88-10.88V166.406c0-5.76-5.12-10.88-10.88-10.88zM563.84 470.406v367.362c0 2.56-.64 7.04-3.84 8.32 0 0-80 56.96-211.84 56.96C188.8 903.048 0 853.128 0 524.166c0-328.96 165.12-396.8 326.4-396.16 139.52 0 195.84 31.36 204.8 37.12 2.56 3.2 3.84 5.76 3.84 8.96l-26.88 113.92c0 5.76-5.76 12.8-12.8 10.88-23.04-7.04-57.6-21.12-138.88-21.12-94.08 0-195.2 26.88-195.2 238.72s96 236.8 165.12 236.8c58.88 0 80-7.04 80-7.04v-147.2h-94.08c-7.04 0-12.16-5.12-12.16-10.88v-117.76c0-5.76 5.12-10.88 12.16-10.88h239.36c7.04 0 12.16 5.12 12.16 10.88z"
      clipRule="evenodd"
    />
  </svg>
);

export const GithubIcon = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg fill="currentColor" viewBox="0 0 24 24" width="20" height="20" className="text-gray-600 hover:text-gray-800" {...props}>
      <path
        fillRule="evenodd"
        d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z"
        clipRule="evenodd"
      />
    </svg>
  )
}

export const FacebookIcon = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
    </svg>
  );
};

export const InstagramIcon = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      fill="none"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      className="lucide lucide-instagram"
      {...props}
    >
      <rect width={20} height={20} x={2} y={2} rx={5} ry={5} />
      <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37zM17.5 6.5h.01" />
    </svg>
  );
};

export const LinkedinIcon = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z" />
      <rect width="4" height="12" x="2" y="9" />
      <circle cx="4" cy="4" r="2" />
    </svg>
  );
};

export const XIcon = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 512 512"
    width="24"
    height="24"
    fill="currentColor"
    {...props} >
    <path d="M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z" />
  </svg>
  );
};

export const Star = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      fill="none"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      className="lucide lucide-star"
      {...props}
    >
      <path d="m12 2 3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
    </svg>
  );
};

export const Logo = ({ ...props }) => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <rect width="100%" height="100%" rx="16" fill="white" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.6482 10.1305L15.8785 7.02583L7.02979 22.5499H10.5278L17.6482 10.1305ZM19.8798 14.0457L18.11 17.1983L19.394 19.4511H16.8453L15.1056 22.5499H24.7272L19.8798 14.0457Z"
      fill="black"
    />
  </svg>
);