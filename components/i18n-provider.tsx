'use client'

import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import '@/lib/i18n'

export function I18nProvider({ children }: { children: React.ReactNode }) {
  const { i18n, ready } = useTranslation()
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (isClient && i18n.language) {
      document.documentElement.lang = i18n.language
    }
  }, [i18n.language, isClient])

  // 在客户端且i18n准备好之前显示加载状态
  if (!isClient || !ready) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-sm text-muted-foreground">Loading...</div>
      </div>
    )
  }

  return <>{children}</>
}