'use client';

import React from 'react';
import Image from 'next/image';

import { cn } from '@/utils/cn';

interface AvatarCirclesProps {
  className?: string;
  numPeople?: number;
  avatarUrls: string[];
}

const AvatarCircles = ({
  numPeople,
  className,
  avatarUrls
}: AvatarCirclesProps) => {
  return (
    <div className={cn('z-10 flex -space-x-4 rtl:space-x-reverse', className)}>
      {avatarUrls.map((url, index) => (
        <Image
          key={index}
          className="h-10 w-10 rounded-full border-2 border-gray-300 dark:border-white"
          src={url}
          width={40}
          height={40}
          alt={`Avatar ${index + 1}`}
          unoptimized
        />
      ))}
      <a
        className="flex h-10 w-10 items-center justify-center rounded-full border-2 border-gray-300 bg-white text-center text-xs font-medium hover:bg-gray-600 dark:border-gray-800 dark:bg-white dark:text-black"
        href=""
      >
        +{numPeople}
      </a>
    </div>
  );
};

export default AvatarCircles;
