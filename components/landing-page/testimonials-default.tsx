'use client';

import { <PERSON>, CardHeader, CardContent } from '@/components/ui/card-header';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { testimonials } from '@/config/testimonials';
import { useTranslation } from 'react-i18next';

export default function Testimonials() {
  const { t } = useTranslation();

  // 本地化推荐数据
  const localizedTestimonials = testimonials.map((testimonial, index) => ({
    ...testimonial,
    title: t(`testimonials.user_${index + 1}_title`, { defaultValue: testimonial.title }),
    text: t(`testimonials.user_${index + 1}_text`, { defaultValue: testimonial.text })
  }));
  const groupedTestimonials = [];
  for (let i = 0; i < localizedTestimonials.length; i += 4) {
    groupedTestimonials.push(localizedTestimonials.slice(i, i + 4));
  }

  return (
    <div className="relative flex flex-col items-center justify-center max-w-5xl p-4 mx-auto">
      <div className="flex items-center w-full max-w-7xl mb-8">
        <div className="flex-grow border-t border-gray-300"></div>
        <div className="flex-shrink px-2 text-center">
          <h2 className="text-3xl font-bold">{t('testimonials.title')}</h2>
          <p className="text-muted-foreground">
            {t('testimonials.subtitle')}
          </p>
        </div>
        <div className="flex-grow border-t border-gray-300"></div>
      </div>
      <div className="flex flex-wrap w-full max-w-7xl">
        {groupedTestimonials.map((group, groupIndex) => (
          <div
            key={groupIndex}
            className="flex flex-col w-full md:w-1/3 p-2 h-fit"
          >
            {group.map((testimonial, index) => (
              <Card key={index} className="mb-4 h-full">
                <CardHeader className="flex flex-row items-center bg-zinc-100 dark:bg-zinc-800 p-2 rounded-t-xl">
                  <div className="flex items-center">
                    <Avatar className="size-7 mr-2"> {/* Adjusted size for smaller avatar */}
                      <AvatarImage src={testimonial.avatarImg} className="h-full w-full" />
                      <AvatarFallback className="h-full w-full">
                        {testimonial.avatarFallback}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex flex-col">
                      <h3 className="text-sm font-semibold">{testimonial.name}</h3>
                      <p className="text-xs text-muted-foreground">{testimonial.title}</p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-4 text-xs">
                  <p>{testimonial.text}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
}
