# Next.js 15 升级指南

## 🚀 快速升级

运行升级脚本：
```bash
./upgrade-nextjs15.sh
```

## 📋 手动升级步骤

如果脚本有问题，按以下步骤手动升级：

### 1. 升级核心包
```bash
npx @next/codemod@canary upgrade latest
npm install react@19 react-dom@19
```

### 2. 迁移代码
```bash
npx @next/codemod@canary next-async-request-api .
```

### 3. 安装依赖并测试
```bash
npm install
npm run build
npm run dev
```

## ⚠️ 可能遇到的问题

### 1. 异步 API 错误
如果看到类似错误：
```
cookies() should be awaited
```

需要手动修改：
```typescript
// 旧代码
const cookieStore = cookies();

// 新代码  
const cookieStore = await cookies();
```

### 2. 缓存问题
如果页面没有按预期缓存，在 `next.config.mjs` 添加：
```javascript
const config = {
  experimental: {
    staleTimes: {
      dynamic: 30, // 恢复页面缓存
    },
  },
};
```

### 3. 构建错误
如果构建失败，检查：
- TypeScript 错误
- 依赖版本冲突
- 导入路径问题

## 🎯 升级后验证

测试以下功能：
- [ ] 登录/注册
- [ ] 仪表板
- [ ] 博客功能
- [ ] 文档系统
- [ ] API 路由

升级完成！🎉