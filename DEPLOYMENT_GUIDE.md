# 🚀 AlsonNote 开发和部署指南

## 📦 前置依赖安装

### 必须安装的工具
```bash
# 1. PNPM (包管理器)
npm install -g pnpm
# 或使用 brew install pnpm

# 2. Docker (Supabase本地开发需要)
# 下载安装：https://www.docker.com/get-started/

# 3. Supabase CLI (数据库管理)
npm install -g @supabase/cli

# 4. Stripe CLI (可选，只有需要测试支付功能才需要)
# 下载安装：https://stripe.com/docs/stripe-cli
```

---

## 🛠️ 本地开发环境搭建

### 步骤 1: 克隆和安装
```bash
cd /Users/<USER>/Dev/AlsonNote
pnpm install
```

### 步骤 2: 环境变量配置
你的 `.env.local` 已经配置好了，无需修改！
- Supabase配置：使用本地Docker容器的默认值
- Stripe配置：如不需要支付功能可暂时留空

### 步骤 3: 启动Supabase本地环境
```bash
# 第一次运行会下载Docker镜像，需要几分钟
pnpm supabase:start

# 成功后会显示：
# API URL: http://127.0.0.1:54321
# DB URL: postgresql://postgres:postgres@127.0.0.1:54322/postgres
# Studio URL: http://127.0.0.1:54323  <- 这是数据库管理界面
# anon key: eyJ...  <- 已在.env.local中配置
# service_role key: eyJ...  <- 已在.env.local中配置
```

### 步骤 4: 启动开发服务器
```bash
pnpm dev
```
访问：http://localhost:3000

### 步骤 5: 数据库管理 (可选)
- Supabase管理面板：http://127.0.0.1:54323
- 可以查看数据表、用户、存储文件等

---

## 💳 Stripe支付功能测试 (可选)

### 如果需要测试支付功能：

1. **注册Stripe账号**：https://dashboard.stripe.com
2. **获取测试密钥**：
   - Dashboard → API keys
   - 复制 `Publishable key` (pk_test_...) 到 `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`
   - 复制 `Secret key` (sk_test_...) 到 `STRIPE_SECRET_KEY`

3. **启动Webhook监听**：
```bash
# 新开一个终端
pnpm stripe:listen
# 会显示 webhook signing secret，复制到 STRIPE_WEBHOOK_SECRET
```

4. **测试支付**：
   - 使用测试卡号：4242 4242 4242 4242
   - 任意未来日期和CVC

---

## 🌍 生产环境部署

### 方案一：Vercel部署 (推荐)

#### 1. Supabase云端配置
1. 访问 https://supabase.com，创建项目
2. 项目创建后，获取：
   - Project URL：https://xxx.supabase.co
   - anon public key：eyJ...

#### 2. 更新生产环境变量
在Vercel或其他平台设置环境变量：
```bash
NEXT_PUBLIC_SITE_URL="https://yourdomain.com"
NEXT_PUBLIC_APP_URL="https://yourdomain.com"
NEXT_PUBLIC_SUPABASE_URL="https://xxx.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your_production_anon_key"
SUPABASE_SERVICE_ROLE_KEY="your_production_service_role_key"

# Stripe生产配置 (可选)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_live_..."
STRIPE_SECRET_KEY="sk_live_..."
STRIPE_WEBHOOK_SECRET="whsec_..."
```

#### 3. 数据库迁移
```bash
# 连接到生产数据库
pnpm supabase:link --project-ref your-project-ref

# 推送本地数据库结构到生产
pnpm supabase:push
```

#### 4. 部署
```bash
# Vercel CLI 部署
vercel --prod

# 或直接在 Vercel Dashboard 连接 GitHub 仓库
```

---

## 🔧 常用命令

### 开发命令
```bash
pnpm dev              # 启动开发服务器
pnpm build            # 构建生产版本 (注意：你暂时不允许运行)
pnpm start            # 启动生产服务器
pnpm lint             # 代码检查
```

### Supabase命令
```bash
pnpm supabase:start          # 启动本地环境
pnpm supabase:stop           # 停止本地环境  
pnpm supabase:restart        # 重启本地环境
pnpm supabase:status         # 查看状态
pnpm supabase:reset          # 重置数据库
```

### Stripe命令
```bash
pnpm stripe:login            # 登录Stripe CLI
pnpm stripe:listen           # 监听webhook事件
```

---

## 🚨 故障排除

### 常见问题

**1. Supabase启动失败**
```bash
# 检查Docker是否运行
docker ps

# 重启Docker服务
# macOS: 重启Docker Desktop
```

**2. 端口冲突**
```bash
# 检查端口占用
lsof -ti:3000
lsof -ti:54321

# 杀死进程
kill -9 <PID>
```

**3. 环境变量问题**
- 检查 `.env.local` 文件是否存在
- 确认没有多余的空格或引号
- 重启开发服务器

---

## 📞 需要帮助？

如果遇到问题，可以：
1. 检查终端错误信息
2. 查看浏览器控制台
3. 访问 Supabase 管理面板检查数据库状态
4. 问我具体的错误信息！😊