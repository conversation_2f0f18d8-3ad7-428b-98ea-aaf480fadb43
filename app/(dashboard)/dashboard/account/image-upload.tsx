'use client'

import { ChangeEvent, useRef, useState, useTransition } from "react";
import { toast } from "@/components/ui/use-toast";
import Image from "next/image";
import { uploadImage } from "@/utils/supabase/storage/client";
import { convertBlobUrlToFile } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { UserIcon } from 'lucide-react';

export function ImageUpload({ user }: { user: any }) {
  const { t } = useTranslation();
  const [avatarUrl, setAvatarUrl] = useState(user.avatar_url ? `${user.avatar_url}?t=${Date.now()}` : null);
  const [imageUrl, setImageUrl] = useState("");
  const imageInputRef = useRef<HTMLInputElement>(null);
  const [isPending, startTransition] = useTransition();
  const router = useRouter();

  const handleImageChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const file = e.target.files[0];
      const newImageUrl = URL.createObjectURL(file);
      setImageUrl(newImageUrl);
    }
  };

  const handleClickUploadImagesButton = async () => {
    if (!user) {
      toast({
        title: t('account.login_required_upload'),
        variant: "destructive",
      });
      return;
    }
    if (!imageUrl.length) {
      toast({
        title: t('account.select_image_first'),
        variant: "destructive",
      });
      return;
    }
    startTransition(async () => {
      const imageFile = await convertBlobUrlToFile(imageUrl);
      const { imageUrl: uploadedImageUrl, error } = await uploadImage({
        file: imageFile,
        bucket: "avatar",
        folder: user.id,
      });
      if (error) {
        toast({
          title: error,
          variant: "destructive",
        });
        return;
      }
      if (uploadedImageUrl) {
        setAvatarUrl(`${uploadedImageUrl}?t=${Date.now()}`);
        await fetch('/api/update-avatar', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ userId: user.id, avatarUrl: uploadedImageUrl }),
        });
        toast({
          title: t('account.image_upload_success'),
          variant: "default",
        });
        setImageUrl("");
        router.refresh();
      }
    });
  };

  return (
    <div className="flex flex-col gap-4 justify-center items-left py-6">
      <span className="text-sm font-medium">{t('account.avatar_image')}</span>
      <div className="ml-1 w-24 h-24 rounded-lg border-2 border-primary p-0.5 flex items-center justify-center bg-muted">
        {imageUrl || avatarUrl ? (
          <Image 
            src={imageUrl || avatarUrl!} 
            width={96} 
            height={96} 
            alt="User Avatar" 
            className="object-cover rounded-lg"
            unoptimized
            key={avatarUrl}
          />
        ) : (
          <UserIcon className="w-12 h-12 text-muted-foreground" />
        )}
      </div>
      <input
        type="file"
        hidden
        ref={imageInputRef}
        onChange={handleImageChange}
        disabled={isPending}
      />
      <div className="space-y-2">
        <Button
          variant="outline"
          onClick={() => imageInputRef.current?.click()}
          disabled={isPending}
          className="mt-2"
        >
          {t('account.select_new_image')}
        </Button>

        {imageUrl && (
          <Button
            onClick={handleClickUploadImagesButton}
            variant="default"
            disabled={isPending}
            className="ml-2"
          >
            {isPending ? t('account.uploading') : t('account.upload_image')}
          </Button>
        )}
      </div>
      <p className="text-sm text-gray-500">
        {!imageUrl && !isPending && t('account.select_image_help')}
        {imageUrl && !isPending && t('account.click_upload_help')}
        {isPending && t('account.uploading_help')}
      </p>
    </div>
  );
}