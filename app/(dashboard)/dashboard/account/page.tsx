import { createClient } from '@/utils/supabase/server';
import {
  getUser,
  getUserDetails,
  getSubscription
} from '@/utils/supabase/queries';
import { redirect } from 'next/navigation';
import { AccountPageClient } from './account-page-client';

export default async function AccountPage() {
  const supabase = createClient();
  const [user, userDetails] = await Promise.all([
    getUser(supabase),
    getUserDetails(supabase),
  ]);

  const subscription = user ? await getSubscription(supabase, user.id) : null;

  if (!user) {
    return redirect('/signin');
  }

  const isSubscribed = subscription?.status === 'active';

  return (
    <AccountPageClient
      user={user}
      userDetails={userDetails}
      subscription={subscription}
      isSubscribed={isSubscribed}
    />
  );
}
