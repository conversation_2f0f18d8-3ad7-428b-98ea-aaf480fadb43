import { LockIcon, Trash2Icon } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { createClient } from '@/utils/supabase/server';
import {
  getUser,
  getUserDetails,
  getSubscription
} from '@/utils/supabase/queries';
import { updateName, updateEmail } from '@/utils/auth-helpers/server';
import { ImageUpload } from './image-upload';
import { redirect } from 'next/navigation';
import { getServerTranslation } from '@/lib/server-i18n';

export default async function AccountPage() {
  const t = getServerTranslation();
  const supabase = createClient();
  const [user, userDetails] = await Promise.all([
    getUser(supabase),
    getUserDetails(supabase),
  ]);

  const subscription = user ? await getSubscription(supabase, user.id) : null;

  if (!user) {
    return redirect('/signin');
  }

  const isSubscribed = subscription?.status === 'active';

  return (
    <div className="flex min-h-screen flex-col bg-muted/40 gap-4">
      <div className="grid auto-rows-max items-start gap-4 md:gap-8 lg:col-span-2">
        <Card className="flex flex-col gap-4">
          <CardHeader>
            <CardTitle>{t('account.personal_info_title')}</CardTitle>
            <CardDescription>
              {t('account.personal_info_description')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form action={updateName} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="fullName">{t('account.full_name')}</Label>
                <Input
                  id="fullName"
                  name="fullName"
                  defaultValue={user.user_metadata.full_name || ''}
                  placeholder={t('account.full_name_placeholder')}
                />
              </div>
              <Button type="submit">{t('account.update_name')}</Button>
            </form>
            <form action={updateEmail} className="space-y-4 mt-6">
              <div className="space-y-2">
                <Label htmlFor="newEmail">{t('account.email')}</Label>
                <Input
                  id="newEmail"
                  name="newEmail"
                  type="email"
                  defaultValue={user.email}
                  placeholder={t('account.email_placeholder')}
                />
              </div>
              <Button type="submit">{t('account.update_email')}</Button>
            </form>
            {/* Pass userDetails to ImageUpload */}
            <ImageUpload user={userDetails} />
          </CardContent>
        </Card>
        {isSubscribed ? (
          <Card className="flex flex-col gap-4 w-full">
            <CardHeader>
              <CardTitle>{t('account.subscription_title')}</CardTitle>
              <CardDescription>
                {t('account.subscription_description')}
              </CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-2 gap-4">
              <div className="flex flex-col gap-2">
                <Label htmlFor="plan">{t('account.plan')}</Label>
                <div className="text-muted-foreground">
                  {subscription?.prices?.products?.name || t('account.na')}
                </div>
              </div>
              <div className="flex flex-col gap-2">
                <Label htmlFor="renewal">{t('account.next_renewal')}</Label>
                <div className="text-muted-foreground">
                  {subscription?.current_period_end
                    ? new Date(subscription.current_period_end).toLocaleDateString(
                        'en-US',
                        { month: 'long', day: 'numeric', year: 'numeric' }
                      )
                    : t('account.na')}
                </div>
              </div>
              <div className="flex flex-col gap-2">
                <Label htmlFor="amount">{t('account.amount')}</Label>
                <div className="text-muted-foreground">
                  {subscription?.prices?.unit_amount
                    ? `$${(subscription.prices.unit_amount / 100).toFixed(2)} / ${subscription.prices.interval}`
                    : t('account.na')}
                </div>
              </div>
              <div className="flex flex-col gap-2">
                <Label htmlFor="status">{t('account.status')}</Label>
                <div className="text-muted-foreground capitalize">
                  {subscription?.status || t('account.na')}
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button>{t('account.manage_subscription')}</Button>
            </CardFooter>
          </Card>
        ) : (
          <Card className="flex flex-col gap-4">
            <CardHeader>
              <CardTitle>{t('account.no_active_subscription')}</CardTitle>
              <CardDescription>
                {t('account.no_active_subscription_description')}
              </CardDescription>
            </CardHeader>
            <CardContent className="grid gap-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-lg font-medium">{t('account.explore_plans')}</h4>
                  <p className="text-muted-foreground">{t('account.check_pricing')}</p>
                </div>
                <Link href="/pricing">
                  <Button>{t('account.view_pricing')}</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        )}
        <Card>
          <CardHeader>
            <CardTitle>{t('account.security')}</CardTitle>
            <CardDescription>
              {t('account.security_description')}
            </CardDescription>
          </CardHeader>
          <CardContent className="grid gap-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <LockIcon className="h-5 w-5" />
                <div>
                  <p className="text-sm font-medium">
                    {t('account.two_factor_auth')}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {t('account.two_factor_description')}
                  </p>
                </div>
              </div>
              <Switch id="two-factor-auth" />
            </div>
            <div className="grid gap-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Trash2Icon className="h-5 w-5" />
                  <div>
                    <p className="text-sm font-medium">{t('account.danger_zone')}</p>
                    <p className="text-sm text-muted-foreground">
                      {t('account.danger_description')}
                    </p>
                  </div>
                </div>
                <Button variant="destructive" size="sm">
                  {t('common.delete')}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
