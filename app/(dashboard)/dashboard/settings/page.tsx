'use client';
import { useTheme } from 'next-themes';
import { useTranslation } from 'react-i18next';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent
} from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { Icons } from '@/components/icons';
import { AtSign, Bell, EyeOff } from 'lucide-react';

export default function SettingsPage() {
  const [isEverythingToggled, setIsEverythingToggled] = useState(false);
  const [isAvailableToggled, setIsAvailableToggled] = useState(true);
  const [isNotificationToggled, setIsNotificationToggled] = useState(true);
  const { setTheme } = useTheme();
  const { t, i18n } = useTranslation();

  const handleLanguageChange = (value: string) => {
    i18n.changeLanguage(value);
  };

  return (
    <div className="w-full mx-auto">
      <div className="space-y-4">
        <div>
          <h1 className="text-3xl font-bold">{t('settings.title')}</h1>
          <p className="text-muted-foreground">
            {t('settings.description')}
          </p>
        </div>
        <div className="grid gap-6">
          <Card className="w-full">
            <CardHeader>
              <CardTitle>{t('settings.notifications_title')}</CardTitle>
              <CardDescription>
                {t('settings.notifications_description')}
              </CardDescription>
            </CardHeader>
            <CardContent className="grid gap-2">
              <div
                className={`flex items-center justify-between p-2 ${isEverythingToggled ? 'bg-accent text-accent-foreground rounded-md' : ''} transition-colors ease-in-out duration-300`}
              >
                <div className="flex items-center space-x-2">
                  <Bell className="h-5 w-5" />
                  <div>
                    <p className="text-sm font-medium">{t('settings.notifications_everything')}</p>
                    <p className="text-sm text-muted-foreground">
                      {t('settings.notifications_everything_description')}
                    </p>
                  </div>
                </div>
                <Switch
                  id="notification-everything"
                  checked={isEverythingToggled}
                  onCheckedChange={setIsEverythingToggled}
                />
              </div>
              <div
                className={`flex items-center justify-between p-2 ${isAvailableToggled ? 'bg-accent text-accent-foreground rounded-md' : ''} transition-colors ease-in-out duration-300`}
              >
                <div className="flex items-center space-x-2">
                  <AtSign className="h-5 w-5" />
                  <div>
                    <p className="text-sm font-medium">{t('settings.notifications_available')}</p>
                    <p className="text-sm text-muted-foreground">
                      {t('settings.notifications_available_description')}
                    </p>
                  </div>
                </div>
                <Switch
                  id="notification-available"
                  defaultChecked
                  checked={isAvailableToggled}
                  onCheckedChange={setIsAvailableToggled}
                />
              </div>
              <div
                className={`flex items-center justify-between p-2 ${isNotificationToggled ? 'bg-accent text-accent-foreground rounded-md' : ''} transition-colors ease-in-out duration-300`}
              >
                <div className="flex items-center space-x-2">
                  <EyeOff className="h-5 w-5" />
                  <div>
                    <p className="text-sm font-medium">{t('settings.notifications_ignoring')}</p>
                    <p className="text-sm text-muted-foreground">
                      {t('settings.notifications_ignoring_description')}
                    </p>
                  </div>
                </div>
                <Switch
                  id="notification-ignoring"
                  checked={isNotificationToggled}
                  onCheckedChange={setIsNotificationToggled}
                />
              </div>
            </CardContent>
          </Card>
          <Card className="w-full">
            <CardHeader>
              <CardTitle>{t('settings.language_title')}</CardTitle>
              <CardDescription>
                {t('settings.language_description')}
              </CardDescription>
            </CardHeader>
            <CardContent className="grid gap-2">
              <div className="grid gap-2">
                <Label htmlFor="language">{t('settings.language_label')}</Label>
                <Select value={i18n.language} onValueChange={handleLanguageChange}>
                  <SelectTrigger className="text-foreground">
                    <SelectValue placeholder={t('settings.language_placeholder')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">
                      <span className="flex items-center gap-2">
                        {t('settings.language_english')}
                        {i18n.language === 'en' && (
                          <span className="ml-auto text-primary">✓</span>
                        )}
                      </span>
                    </SelectItem>
                    <SelectItem value="zh">
                      <span className="flex items-center gap-2">
                        {t('settings.language_chinese')}
                        {i18n.language === 'zh' && (
                          <span className="ml-auto text-primary">✓</span>
                        )}
                      </span>
                    </SelectItem>
                    <SelectItem value="ja">
                      <span className="flex items-center gap-2">
                        {t('settings.language_japanese')}
                        {i18n.language === 'ja' && (
                          <span className="ml-auto text-primary">✓</span>
                        )}
                      </span>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
          <Card className="w-full">
            <CardHeader>
              <CardTitle>{t('settings.appearance_title')}</CardTitle>
              <CardDescription>
                {t('settings.appearance_description')}
              </CardDescription>
            </CardHeader>
            <CardContent className="grid gap-2">
              <div className="grid gap-2">
                <Label htmlFor="theme">{t('settings.appearance_theme')}</Label>
                <Select onValueChange={(value) => setTheme(value)}>
                  <SelectTrigger className="text-muted-foreground">
                    <SelectValue placeholder={t('settings.appearance_theme_placeholder')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">
                      <div className="flex items-center">
                        <Icons.sun className="mr-2 h-4 w-4" />
                        <span>{t('settings.appearance_light')}</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="dark">
                      <div className="flex items-center">
                        <Icons.moon className="mr-2 h-4 w-4" />
                        <span>{t('settings.appearance_dark')}</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="system">
                      <div className="flex items-center">
                        <Icons.laptop className="mr-2 h-4 w-4" />
                        <span>{t('settings.appearance_system')}</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="font">{t('settings.appearance_font')}</Label>
                <Select>
                  <SelectTrigger className="text-muted-foreground">
                    <SelectValue placeholder={t('settings.appearance_font_placeholder')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sans">{t('settings.appearance_sans')}</SelectItem>
                    <SelectItem value="serif">{t('settings.appearance_serif')}</SelectItem>
                    <SelectItem value="mono">{t('settings.appearance_mono')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}