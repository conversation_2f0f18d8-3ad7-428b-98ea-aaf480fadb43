import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import {
  getUser,
  getUserDetails,
  getSubscription
} from '@/utils/supabase/queries';

export async function GET() {
  try {
    const supabase = createClient();
    const [user, userDetails] = await Promise.all([
      getUser(supabase),
      getUserDetails(supabase),
    ]);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const subscription = await getSubscription(supabase, user.id);

    return NextResponse.json({
      user,
      userDetails,
      subscription
    });
  } catch (error) {
    console.error('Error fetching account data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
