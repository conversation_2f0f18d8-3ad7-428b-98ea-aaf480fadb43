import { cookies, headers } from 'next/headers';
import en from '../i18n/en.json';
import zh from '../i18n/zh.json';
import ja from '../i18n/ja.json';

type TranslationResources = {
  en: typeof en;
  zh: typeof zh;
  ja: typeof ja;
};

const resources: TranslationResources = {
  en,
  zh,
  ja,
};

type SupportedLanguage = keyof TranslationResources;

// 从嵌套对象中获取值的工具函数
function getNestedValue(obj: any, path: string): string | undefined {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

// 获取用户语言偏好
export function getServerLanguage(): SupportedLanguage {
  try {
    // 首先尝试从 cookies 获取用户设置的语言
    const cookieStore = cookies();
    const savedLanguage = cookieStore.get('i18nextLng')?.value;
    
    if (savedLanguage && savedLanguage in resources) {
      return savedLanguage as SupportedLanguage;
    }

    // 如果没有保存的语言偏好，从 Accept-Language header 获取
    const headersList = headers();
    const acceptLanguage = headersList.get('accept-language');
    
    if (acceptLanguage) {
      // 解析 Accept-Language header
      const languages = acceptLanguage
        .split(',')
        .map(lang => {
          const [code, q = '1'] = lang.trim().split(';q=');
          return { code: code.split('-')[0], quality: parseFloat(q) };
        })
        .sort((a, b) => b.quality - a.quality);

      // 找到第一个支持的语言
      for (const { code } of languages) {
        if (code in resources) {
          return code as SupportedLanguage;
        }
      }
    }
  } catch (error) {
    // 在某些情况下（如静态生成），headers 可能不可用
    console.warn('Unable to detect language from headers:', error);
  }

  // 默认返回英文
  return 'en';
}

// 服务端翻译函数
export function getServerTranslation(language?: SupportedLanguage) {
  const lang = language || getServerLanguage();
  const translations = resources[lang];

  return function t(key: string, defaultValue?: string): string {
    const value = getNestedValue(translations, key);
    return value || defaultValue || key;
  };
}

// 便捷函数，自动检测语言
export function serverT(key: string, defaultValue?: string): string {
  const t = getServerTranslation();
  return t(key, defaultValue);
}
