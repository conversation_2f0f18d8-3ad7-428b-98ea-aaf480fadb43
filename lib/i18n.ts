import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'

import en from '../i18n/en.json'
import zh from '../i18n/zh.json'
import ja from '../i18n/ja.json'

const resources = {
  en: {
    translation: en,
  },
  zh: {
    translation: zh,
  },
  ja: {
    translation: ja,
  },
}

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    debug: false,
    
    // 支持的语言白名单
    supportedLngs: ['en', 'zh', 'ja'],
    
    // 非严格语言匹配，自动映射相似语言代码
    load: 'languageOnly',
    
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag', 'path', 'subdomain'],
      caches: ['localStorage'],
      // 支持的语言映射
      lookupFromPathIndex: 0,
    },
    
    interpolation: {
      escapeValue: false,
    },
    
    defaultNS: 'translation',
    
    // 解决 SSR hydration 问题
    react: {
      useSuspense: false,
    },
  })

export default i18n