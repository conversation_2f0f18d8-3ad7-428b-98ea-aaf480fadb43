<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="6" height="6" viewBox="0 0 6 6" xml:space="preserve">
    <defs>
        <pattern id="pattern-35" patternUnits="userSpaceOnUse" width="6" height="6">
    <circle cx="0" cy="0" r="1" fill="currentcolor" style="fill: var(--pattern-channel-1, currentcolor)" />
    <circle cx="0" cy="6" r="1" fill="currentcolor" style="fill: var(--pattern-channel-1, currentcolor)" />
    <circle cx="6" cy="6" r="1" fill="currentcolor" style="fill: var(--pattern-channel-1, currentcolor)" />
    <circle cx="6" cy="0" r="1" fill="currentcolor" style="fill: var(--pattern-channel-1, currentcolor)" />
    <circle cx="3" cy="3" r="1" fill="currentcolor" style="fill: var(--pattern-channel-1, currentcolor)" />
</pattern>
    </defs>
    <rect x="0" y="0" width="6" height="6" fill="url(#pattern-35)" />
</svg>