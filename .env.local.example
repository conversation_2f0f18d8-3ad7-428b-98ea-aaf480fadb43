# ==== 应用基础配置 ====
NEXT_PUBLIC_SITE_URL="http://localhost:3000"
# 作用: 网站的公开访问地址，用于生成链接、重定向等
# 开发: http://localhost:3000
# 生产: https://yourdomain.com

NEXT_PUBLIC_APP_URL="http://localhost:3000"  
# 作用: 应用的基础URL，主要用于API调用和内部链接
# 开发: http://localhost:3000
# 生产: https://yourdomain.com

# ==== Supabase数据库配置 ====
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
# 作用: 客户端访问Supabase的公开密钥，用于用户认证、查询数据等
# 开发: 使用上面的默认值（本地Docker容器）
# 生产: 从supabase.com项目设置中获取，形如 eyJ... 

NEXT_PUBLIC_SUPABASE_URL="http://127.0.0.1:54321"
# 作用: Supabase服务的访问地址
# 开发: http://127.0.0.1:54321 (本地Docker容器地址)
# 生产: https://your-project.supabase.co (从supabase.com获取)

SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU"
# 作用: 服务器端操作的超级管理员密钥，可绕过所有安全规则
# 开发: 使用上面的默认值
# 生产: 从supabase.com项目设置中获取（非常重要，不能泄露！）

# ==== Stripe支付配置（可选，测试支付功能时需要）====
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
# 作用: 客户端调用Stripe支付组件的公开密钥
# 获取: Stripe Dashboard → API keys → Publishable key (pk_test_...)
# 注意: 测试环境用pk_test_开头，生产用pk_live_开头

STRIPE_SECRET_KEY=
# 作用: 服务器端调用Stripe API的私密密钥
# 获取: Stripe Dashboard → API keys → Secret key (sk_test_...)  
# 注意: 测试环境用sk_test_开头，生产用sk_live_开头，绝对不能泄露！

STRIPE_WEBHOOK_SECRET=
# 作用: 验证Stripe发送的webhook事件是否合法
# 获取: 创建webhook端点后自动生成 (whsec_...)
# 用途: 防止恶意请求伪造支付成功事件