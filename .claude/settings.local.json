{"permissions": {"allow": ["mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(find:*)", "Bash(grep:*)", "Bash(for:*)", "Bash(done)", "mcp__firecrawl__firecrawl_search", "mcp__firecrawl__firecrawl_scrape", "mcp__chrome-mcp__chrome_get_web_content", "mcp__chrome-mcp__chrome_network_debugger_start", "mcp__chrome-mcp__chrome_console", "mcp__playwright__browser_navigate", "mcp__chrome-mcp__chrome_navigate", "mcp__chrome-mcp__get_windows_and_tabs", "Bash(pnpm add:*)", "WebSearch", "WebSearch"], "deny": [], "ask": []}}