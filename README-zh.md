# 🚀 AlsonNote - AI 智能笔记应用

一款基于 AI 的智能笔记应用，支持语音转文本、智能整理和 AI 助手功能。

> **注意**: 这是一个私有项目，目前处于开发阶段。

## 🎉 功能特性

- 🔐 **完整认证系统**: 使用 [Supabase](https://supabase.io/docs/guides/auth) 进行安全的用户管理和身份验证
- 🛠️ **数据管理**: 基于 PostgreSQL 的强大数据访问和管理工具，使用 [Supabase](https://supabase.io/docs/guides/database)
- 💳 **Stripe 集成**: 与 [Stripe Checkout](https://stripe.com/docs/payments/checkout) 和 [Stripe 客户门户](https://stripe.com/docs/billing/subscriptions/customer-portal) 无缝集成
- 🌐 **价格和订阅**: 通过 [Stripe webhooks](https://stripe.com/docs/webhooks) 自动同步价格计划和订阅状态
- 🌈 **TailwindCSS & Tailwind UI**: 可定制的灵活UI组件，基于 Tailwind UI
- ⚛️ **React 18**: 享受 React 18 的最新功能和改进
- 📘 **TypeScript**: 强类型编程，提供更好的开发体验
- 🎨 **Shadcn/ui**: 美观且可定制的UI组件
- 🔍 **Zod 验证**: 模式验证，确保数据安全可靠
- 🧪 **测试工具**: 使用 Jest、React Testing Library 和 Playwright 进行集成单元测试和端到端测试
- 🧑‍💻 **开发者体验**: ESLint、Prettier、Husky 和 Commitlint 保持代码质量和一致性
- 📀 **Supabase 存储** - 包括设置策略和在浏览器中处理图像压缩
- ⼬ **tRPC** - 包含如何为您的API添加精简的tRPC路由器指南。您只需要添加3个文件夹和1个文件
- ⚙️ **本地开发**: 使用 Supabase、Docker 和一套自定义命令进行本地开发
- 📚 **文档和博客**: 利用开源项目 Fumadocs 的 MDX 编译器创建文档和博客内容

## 🎬 演示

[在线演示](https://hikari.antoineross.com/)

**首页:** ![演示截图](./public/hikari-landingpage.png)

**仪表板视图:** ![仪表板截图](./public/hikari-dashboard.png)

**价格表:** ![价格截图](./public/hikari-pricing.png)

**文档:** ![文档截图](./public/hikari-documentation.png)

**博客:** ![博客截图](./public/hikari-blog.png)

## 📄 快速开始指南

按照 [快速开始指南](https://hikari.antoineross.com/docs/quick-start) 快速启动和运行。

## 🚀 上线部署

### **1. 归档测试产品**

在正式上线之前，请归档所有测试模式的 Stripe 产品。将 Stripe 从测试模式切换到生产模式，并更新您的环境变量。

### **2. 重新部署**

更新环境变量后，通过 Vercel 重新部署您的应用程序。

## 📚 额外功能

- 📈 **分析就绪**: 轻松集成 Google Analytics 等分析工具
- 🌐 **国际化支持**: 使用 Paraglide 内置国际化
- 🔥 **Lighthouse 性能**: 通过优化的性能、可访问性和SEO获得完美分数

## 📞 联系我们

如有任何问题或建议，请联系我们。

由 [Alson](https://alson.dev) 开发。